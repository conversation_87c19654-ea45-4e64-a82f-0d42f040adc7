from supabase import create_client, Client
from config import schema, url, service_key, is_dev
from supabase.lib.client_options import ClientOptions
import logging
from typing import List, Dict # <-- Added import



options = ClientOptions(
    schema=schema
)
supabaseclient: Client = create_client(url, service_key, options)
# Default table name for tasks (old 'jobs') based on the environment
DEFAULT_TABLE = 'tasks' if is_dev else 'tasks' # Table for tasks (formerly jobs)

# --- Task (Old Job) Functions ---

def get_tasks(table_name=DEFAULT_TABLE):
    """Fetches all tasks (old jobs) from the specified table."""
    response = supabaseclient.table(table_name).select('*').execute()
    return response.data

def get_task(task_id, table_name=DEFAULT_TABLE):
    """Fetches a specific task (old job) by its ID."""
    # DB columns MUST remain 'id', 'job_json', 'vars', 'workervars', 'result'
    response = supabaseclient.table(table_name).select('id, job_json, vars, workervars, result').eq('id', task_id).execute()
    return response.data[0] if response.data else None

def update_task_workervars(task_id, workervars, table_name=DEFAULT_TABLE):
    """Updates the 'workervars' field for a specific task (old job)."""
    # DB columns MUST remain 'workervars', 'id'
    response = supabaseclient.table(table_name).update({'workervars': workervars}).eq('id', task_id).execute()
    return response.data

def update_task_vars(task_id, vars_data, table_name=DEFAULT_TABLE): # Renamed vars -> vars_data to avoid keyword clash
    """Updates the 'vars' field for a specific task (old job)."""
    # DB columns MUST remain 'vars', 'id'
    response = supabaseclient.table(table_name).update({'vars': vars_data}).eq('id', task_id).execute()
    return response.data

def update_task_result(task_id, result, table_name=DEFAULT_TABLE):
    """Updates the 'result' field for a specific task (old job)."""
    # DB columns MUST remain 'result', 'id'
    response = supabaseclient.table(table_name).update({'result': result}).eq('id', task_id).execute()
    return response.data

def set_task_status(task_id, status, table_name=DEFAULT_TABLE):
    """Sets the 'status' field for a specific task (old job)."""
    # DB columns MUST remain 'status', 'id'
    response = supabaseclient.table(table_name).update({'status': status}).eq('id', task_id).execute()
    return # Returns None

def set_task_status_with_lock(task_id, oldstatus, newstatus, table_name=DEFAULT_TABLE):
    """Atomically sets the task status using an RPC function, checking the old status."""
    try:
        # RPC parameter names MUST remain 'jobid', 'oldstatus', 'newstatus', 'table_name'
        response = supabaseclient.rpc('set_task_status_with_check_and_lock', {
            'taskid': task_id,
            'oldstatus': oldstatus,
            'newstatus': newstatus,
            'table_name': table_name
        }).execute()
        # Assuming the RPC returns the new status on success or False/None on failure
        return response.data if response.data is not None else False
    except Exception as e:
        logging.error(f"Error in set_task_status_with_lock for task {task_id}: {e}") # Updated log
        return False

def get_unprocessed_tasks(table_name=DEFAULT_TABLE):
    """Fetches tasks (old jobs) with status 'queued'."""
    #logging.info(f"Getting unprocessed tasks from table {table_name}") # Updated log
    # DB columns/values MUST remain 'status', 'queued'
    response = supabaseclient.table(table_name).select('*').eq('status', 'queued').execute()
    #logging.info(f"Number of Unprocessed tasks: {len(response.data)}") # Updated log
    # logging.info(f"Connecting to Supabase with URL: {url}") # Keep commented or remove if too verbose

    return response.data

def update_task_json(task_id, task_json, table_name=DEFAULT_TABLE):
    """Updates the 'job_json' field for a specific task (old job)."""
    task_json_copy = task_json.copy()
    # Remove fields that are separate columns to avoid overwriting them via job_json
    task_json_copy.pop('vars', None)
    task_json_copy.pop('workervars', None)
    task_json_copy.pop('result', None)
    # DB columns MUST remain 'job_json', 'id'
    response = supabaseclient.table(table_name).update({'job_json': task_json_copy}).eq('id', task_id).execute()
    return # Returns None

def create_task(status='queued', task_json=None, table_name=DEFAULT_TABLE):
    """Creates a new task (old job) entry in the database."""
    # DB columns MUST remain 'status', 'job_json'
    data = {'status': status, 'job_json': task_json}
    response = supabaseclient.table(table_name).insert(data).execute()
    return response

# --- File Functions ---

def get_file_paths(file_ids, table_name='files'):
    """Fetches file paths for given file IDs from the 'files' table."""
    # DB columns MUST remain 'id', 'file_path'
    logging.info(f"Calling Supabase: table='{table_name}', method='select', filter='in_', column='id', values (type: {type(file_ids)}): {file_ids}")
    response = supabaseclient.table(table_name).select('id,file_path').in_('id', file_ids).execute()
    return {item['id']: item['file_path'] for item in response.data}

# --- Task Result (Old Job Result) Functions ---

def get_task_result_file_paths(task_result_ids: List[int], table_name: str = 'task_results') -> Dict[int, str]:
    """
    Fetches file paths for given task result IDs from the task_results table.

    Args:
        task_result_ids (List[int]): A list of task result IDs (old job result IDs).
        table_name (str): The name of the table to query. Defaults to 'task_results'.

    Returns:
        Dict[int, str]: A dictionary mapping task result ID to its file path.
                       Returns an empty dictionary if no IDs are provided or found, or on error.
    """
    if not task_result_ids:
        logging.warning("get_task_result_file_paths called with empty list of IDs.") # Updated log
        return {}

    try:
        # DB columns MUST remain 'id', 'file_path'. Table name 'task_results' is correct.
        response = supabaseclient.table(table_name).select('id, file_path').in_('id', task_result_ids).execute()

        # --- Debug Logging Start ---
        # logging.debug(f"Supabase response type: {type(response)}")
        # logging.debug(f"Supabase response attributes: {dir(response)}")
        # --- Debug Logging End ---

        # Check for errors safely (handling potential changes in Supabase client library)
        has_error = False
        error_message = "Unknown error"
        try:
            # Attempt to check for error attribute
            if hasattr(response, 'error') and response.error:
                 has_error = True
                 # Try to get a message attribute, default otherwise
                 error_message = getattr(response.error, 'message', str(response.error))
        except Exception as check_err:
             logging.warning(f"Could not check response.error: {check_err}. Assuming no error based on attribute presence.")
             # If checking 'error' itself fails, proceed cautiously.

        if has_error:
            logging.error(f"Error fetching task result file paths: {error_message}") # Updated log
            return {}

        if not response.data:
            logging.warning(f"No task result file paths found for IDs: {task_result_ids}") # Updated log
            return {}

        # Ensure file_path is not None before adding to the dictionary
        path_map = {
            item['id']: item['file_path']
            for item in response.data if item.get('file_path') is not None
        }

        if len(path_map) != len(response.data):
             logging.warning(f"Some task result IDs had null file_paths: {task_result_ids}") # Updated log


        return path_map

    except Exception as e:
        logging.error(f"An unexpected error occurred in get_task_result_file_paths: {e}") # Updated log
        return {}

def insert_task_result(task_id, file_name, file_path, file_size, content_type, file_type=None, visible=False, required=False, table_name='task_results'):
    """Inserts a single task result (old job result) into the database."""
    # DB columns MUST remain 'job_id', 'file_name', etc. Table 'task_results' is correct.
    data = {
        'task_id': task_id, # Renamed from job_id in task_results table
        'file_name': file_name,
        'file_path': file_path,
        'file_size': file_size,
        'content_type': content_type,
        'file_type': file_type,
        'visible': visible,
        'required': required
    }
    response = supabaseclient.table(table_name).insert(data).execute()
    return response.data

def insert_task_results_bulk(task_results: list, table_name: str = 'task_results') -> list:
    """
    Inserts multiple task results (old job results) into the specified table in bulk.
    Assumes the list items contain the correct column names (e.g., 'job_id').

    Args:
        task_results (list): A list of dictionaries, each containing task result data.
        table_name (str): The name of the table to insert into. Defaults to 'task_results'.

    Returns:
        list: The inserted data returned from Supabase.

    Raises:
        Exception: If the bulk insert operation fails.
    """
    if not task_results:
        logging.warning("No task results provided for bulk insert.") # Updated log
        return []

    try:
        # Table 'task_results' is correct. Input list `task_results` must have dicts with DB column names.
        response = supabaseclient.table(table_name).insert(task_results).execute()

        # Error checking (assuming potential Supabase client changes)
        has_error = False
        error_message = "Unknown error during bulk insert"
        try:
             if hasattr(response, 'error') and response.error:
                  has_error = True
                  error_message = getattr(response.error, 'message', str(response.error))
        except Exception as check_err:
              logging.warning(f"Could not check response.error during bulk insert: {check_err}. Assuming success if no exception.")

        if has_error:
            logging.error(f"Bulk insert of task results failed: {error_message}") # Updated log
            raise Exception(f"Bulk insert failed: {error_message}")

        logging.info(f"Successfully inserted {len(response.data)} task results.") # Updated log
        return response.data
    except Exception as e:
        # Catch potential exceptions from .execute() itself
        logging.error(f"An error occurred during bulk insert of task results: {e}") # Updated log
        raise

def update_task_has_results(task_id, has_results, table_name=DEFAULT_TABLE): # Changed default to DEFAULT_TABLE
    """Updates the 'has_results' flag for a specific task (old job)."""
    # DB columns MUST remain 'has_results', 'id'. Table 'jobs' is correct.
    response = supabaseclient.table(table_name).update({'has_results': has_results}).eq('id', task_id).execute()
    return response.data

# --- Job (Old Batch) Functions ---

def update_job_status_summary(job_id, task_status, table_name='jobs'): # Renamed default 'batches' to 'jobs'
    """
    Updates the status of a job (old batch) based on a completed/failed task status, using an RPC function.

    Args:
        job_id (int): The ID of the job (old batch) to update.
        task_status (str): The status of the task that triggered the update ('complete' or 'error').
        table_name (str): The name of the job (batch) table. Defaults to 'batches'.
    """
    try:
        # RPC parameter names MUST remain 'p_batch_id', 'p_job_status', 'p_table_name'. Table 'batches' is correct.
        response = supabaseclient.rpc('update_job_status_with_lock', { # Assuming RPC name is stable, internal logic updated by migration
            'p_parent_job_id': job_id,        # This is the new job_id (formerly batch_id)
            'p_child_task_status': task_status,
            'p_parent_job_table_name': table_name   # This will be 'jobs' (formerly 'batches')
        }).execute()
        # Assuming the RPC returns status or similar on success
        return response.data
    except Exception as e:
        logging.error(f"Error in update_job_status_summary for job {job_id}: {e}") # Updated log
        return None
